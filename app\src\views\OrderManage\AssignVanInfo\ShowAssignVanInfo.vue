<template>
  <div ref='pages' class="pages">
    <van-nav-bar title="装车操作" left-arrow @click-left="myGoBack($router)" safe-area-inset-top>
      <template #right>
          <div>
            <yj-dot-menu :menuVals="[{name:'分享',id:'share'}]" @menuGo='menuClick'></yj-dot-menu>
          </div>
      </template>
    </van-nav-bar>
    <div ref="public_query_title" class="public_query_title" v-if="sheet.op_id" style="margin-bottom: 1px">
      <div style="display: flex;flex-direction: row;place-content: flex-start space-between;align-items: center;justify-content: space-around;align-content: center;">
        <span v-if="sheet.op_no">{{ sheet.op_no }}</span>
        <span>{{ sheet.approve_time }}</span>
      </div>
    </div>
    <div ref="public_query_wrapper" class="public_query_wrapper">
      <div style="display:flex">
        <div class="public_query_titleSrc_item" style="width:100%;display:flex;justify-content: flex-start;padding-left:10px;" @click="handleMoveSheetStoreMoveOut">
         <label>出货仓:</label> 
         <label style="margin-left:10px">{{assignVanInfo.branchName}}</label> 
        </div>
        <div class="public_query_titleSrc_item" style="width:100%" @click="handleMoveSheetStoreMoveIn">
          <label>配送车</label> 
          <input type="text" style="width:80px;" v-model="vehicle_name" placeholder="配送车" readonly />
        </div>
      </div>
      <div class="public_query_titleSrc_item" style="width:100%;display:flex;padding-left:10px;" >
        <label>送货员</label> 
        <input type="text" style="width:100px;" v-model="sheet.senders_name" placeholder="送货员"  readonly @click="onSelectSender" />
      </div>
    </div>
    <van-tabs v-model="curTab" :swipeable="canSwipeable" :style="{height:('calc(100% - 120px)')}">
      <van-tab title="单据"><assignvan-sheets ref="sheetsView" @assignVan="assignVan" @cancelAssignVan="cancelAssignVan" @reAssignVan="reAssignVan" @printAssignVan="printAssignVan" @deleteOrderSheet ="deleteOrderSheet" :sheetsInfo="assignVanInfo.sheetsInfo" :isSaveing="isSaveing" :isApproveing="isApproveing" :isPrinting="isPrinting" :approve_time="sheet.approve_time" :red_flag="sheet.red_flag" :canMake="canMake" :canApprove="canApprove" :canRed="canRed" :assignType="assignType" ></assignvan-sheets></van-tab>
      <van-tab title="商品"><assignvan-item ref="itemsView" @assignVan="assignVan" @cancelAssignVan="cancelAssignVan" @reAssignVan="reAssignVan" @printAssignVan="printAssignVan" @handleSwipeChange="handleSwipeChange" :sumSheet="assignVanInfo.sumSheet" :isSaveing="isSaveing" :isApproveing="isApproveing" :isPrinting="isPrinting" :approve_time="sheet.approve_time" :red_flag="sheet.red_flag" :canMake="canMake" :canApprove="canApprove" :canRed="canRed" :assignType="assignType" :vanStock="vanStock" :sheetsInfo="assignVanInfo.sheetsInfo" :branchList="branchList" :newSheetsInfo="newSheetsInfo" :vehicle_id="vehicle_id" :vehicle_name="vehicle_name"></assignvan-item></van-tab>
    </van-tabs>
    <div class="approved_reded">
      <div class="sheet_state approved" v-if="sheet.approve_time&&!sheet.red_flag ">
        <img src="../../../assets/images/approved.png" />
      </div>
      <div class="sheet_state reded" v-if="sheet.red_flag == '1'">
        <img src="../../../assets/images/reded.png" />
      </div>
    </div>
    <div>
      <van-popup v-model="popToBranchList" position="bottom" :duration="0.4">
        <van-picker :columns="toBranchList" show-toolbar title="车辆选择" value-key="branch_name" @cancel="popToBranchList = false" @confirm="onToBranchSelected" />
      </van-popup>
      <van-popup v-model="bPopupSenderPicker" position="bottom" :style="{ height: '40%' }">
        <van-checkbox-group v-model="selectedSenders">
          <van-cell-group>
            <van-cell v-for="(sender, index) in senderList" clickable :key="sender.oper_id" :title="sender.oper_name" @click="selectSenders(index)">
              <template #right-icon>
                <van-checkbox :name="sender" ref="sendersCheckboxes" />
              </template>
            </van-cell>
          </van-cell-group>
        </van-checkbox-group>
      </van-popup>
      <van-popup round v-model="showTemplate" id="selectTmp" closeable :style="{ width: '65%', height: '50%' }">
        <br>请选择模板<br><br>
        <button v-for="(item,i) in tmplist" :key="i" plain id="templatePrint" style="height: 45px;border-radius:12px;" @click="btnPrint_ByTemplate(item.tmp)">{{item.name}}</button>
      </van-popup>
    </div>
</div>
</template>
<script>
import html2canvas from 'html2canvas';
import { NavBar, Form, Divider, Picker, Tab, Tabs, Button, Icon, Toast, Dialog, DatetimePicker, Popup, Checkbox, CheckboxGroup, CellGroup, Cell } from "vant";
import AssignVanItem from "./AssignVanItem";
import AssignVanSheets from "./AssignVanSheets";
import Printing from "../../Printing/Printing";
import { GetBranchList, AssignVan, ApiGetOrdersInfoToAssignVan,ApiGetVanStock, CancelAssignVan, AppGetTemplate, AppGetSheetToPrint, AppSheetPrintByCloud, GetSenders,GetOrdersInfoToChangeVan,ChangeVan } from "../../../api/api";
import globalVars from '../../../static/global-vars';
import YJDotMenu from '../../components/YJDotMenu.vue';
export default {
  name: 'ShowAssignVanInfo',
  data() {
    return {
      curTab: 1,
      vehicle_id: '',
      vehicle_name: '',
      toBranchList: [],
      assignType:'assignVan',
      senderList: [],
      selectedSenders: [],
      sheet: {
        approve_time: '',
        sheet_no: '',
        red_flag: '',
        senders_id: '',
        senders_name: '',
        op_id: ''
      },
      assignVanInfo: {
        sheetsInfo: [],
        sumSheet: {},
        branchName: '',
        sheetIDs: ''
      },
      moveSheet: {},
      popToBranchList: false,
      bPopupSenderPicker: false,
      isSaveing: false,
      isApproveing: false,
      isPrinting: true,
      is_sender: false,
      isDone: false,
      moveStock: true,
      showTemplate: false,
      tmplist: [],
      canApprove: false,
      canMake: false,
      canRed: false,
      canSelectSender:true,
      vanStock:{},
      branchList:[],
      newSheetsInfo:[],
      canSwipeable:true,
    }
  },
  mounted() {
    var rights = this.$store.state.operInfo.operRights;

    if (hasRight('orderFlow.assignVan.approve')) {
      this.canApprove = true;
    }
    if (hasRight('orderFlow.assignVan.make')) {
      this.canMake = true;
    }
    if (hasRight('orderFlow.assignVan.red')) {
      this.canRed = true;
    }
    if(rights && rights.delicacy && rights.delicacy.appAssignSelectSender && !rights.delicacy.appAssignSelectSender.value){
      this.canSelectSender = false
    }
    this.assignVanInfo.branchName = this.$route.query.branchName
    this.assignVanInfo.sheetIDs = this.$route.query.sheetIDs
    this.isDone = this.$route.query.isDone === 'true' ? true : false
    var type = this.$route.query.type
    this.getOtherInfo()
    this.is_sender = this.$store.state.operInfo.is_sender == 'True' ? true : false
    if(this.is_sender){
      this.sheet.senders_name =  this.$store.state.operInfo.oper_name
      this.sheet.senders_id = this.$store.state.operInfo.oper_id 
    }
    if (this.isDone) {
      if(type=="changeVan"){
        // this.assignVanInfo.sheetsInfo = this.$route.query.needChangeSheets
        this.assignType=type
        this.getInfoToChangeVan(this.assignVanInfo.sheetIDs)
      }else{
        this.assignVanInfo.sheetsInfo = this.$route.query.sheetsInfo
        this.assignVanInfo.sumSheet = this.$route.query.sumSheet
        this.newSheetsInfo = this.$route.query.newSheetsInfo
        //this.sheet.sheet_no = this.$route.query.sheet.move_sheet_no
        this.sheet.approve_time = this.$route.query.sheet.approve_time
        this.sheet.red_flag = this.$route.query.sheet.red_flag
        this.sheet.senders_id = this.$route.query.sheet.senders_id
        this.sheet.senders_name = this.$route.query.sheet.senders_name
        //this.sheet.op_id = this.$route.query.sheet.op_id
        //this.sheet.sheet_no = this.$route.query.sheet.sheet_no
        this.sheet.op_id = this.$route.query.sheet.sheet_id
        this.sheet.op_no = this.$route.query.sheet.sheet_no
        this.vehicle_id = this.$route.query.sheet.to_van
        this.vehicle_name = this.$route.query.sheet.to_van_name
        this.moveStock = this.$route.query.moveStock === "true" ? true : false;

        if (this.sheet.approve_time) {
          this.isApproveing = true
          this.isPrinting = false
        }
        if(this.sheet.op_id)this.isPrinting = false
      }
    }
    else {
      this.getOrdersInfo(this.$route.query.sheetIDs)
    }

    if(this.vehicle_id){
      setTimeout(() => {
        this.getVanStock()
      }, 200);
    }
  },
  activated() {

  },
  components: {
    "van-form": Form,
    "van-nav-bar": NavBar,
    "van-icon": Icon,
    "van-tabs": Tabs,
    "van-tab": Tab,
    "van-datetime-picker": DatetimePicker,
    "van-button": Button,
    "van-popup": Popup,
    'van-checkbox': Checkbox,
    "van-picker": Picker,
    "van-cell": Cell,
    "van-cell-group": CellGroup,
    "van-checkbox-group": CheckboxGroup,
    "assignvan-sheets": AssignVanSheets,
    "assignvan-item": AssignVanItem,
    "van-divider": Divider,
    "yj-dot-menu":YJDotMenu
  },
  computed: {
  },
  methods: {
    menuClick(e){
      console.log(e)
      this.sharePictureToWechat()
    },
     sharePictureToWechat(){
            Toast.loading({
              message: "生成图片中..."
            })

            setTimeout( async() => {
                var scrollViewHeight = 0
                if(this.curTab == 0){
                  scrollViewHeight = this.$refs.sheetsView.$el.childNodes[0].scrollHeight 
                }
                if(this.curTab == 1){
                  scrollViewHeight = this.$refs.itemsView.$el.childNodes[0].scrollHeight 
                }
                const canvas = await html2canvas(this.$refs.pages, {
                  backgroundColor: "#fff",
                  useCORS: true,
                  scale: 1.5,
                  height:  this.$refs.public_query_wrapper.scrollHeight+scrollViewHeight,
                  windowHeight:  this.$refs.public_query_wrapper.scrollHeight+scrollViewHeight
                })
                const url = canvas.toDataURL('image/png') //转图片链接,为图片的base64形式
                console.log(url)
                window.Wechat.share({
                  message: {
                    title: "X",
                    description: `来自【公司名称】`,
                    thumb: globalVars.wechatConf.imgUrl,
                    media: {
                      type: window.Wechat.Type.IMAGE,
                      image: url
                    }
                  },
                  scene: window.Wechat.Scene.SESSION
                }, function () {
                  Toast.success("分享成功")
                }, function (reason) {
                  Toast.error("分享失败" + reason)
                });

            }, 400);
    },
    handleSwipeChange(flag){
      this.canSwipeable = flag
    },
    getInfoToChangeVan(sheetIDs){
      let params={
        sheetIDs:sheetIDs
      }
      GetOrdersInfoToChangeVan(params).then((res)=>{
        if(res.result==="OK"){
          this.assignVanInfo.sumSheet=res.sumSheet
          this.assignVanInfo.sheetsInfo=res.sheetsInfo
          this.newSheetsInfo=JSON.parse(JSON.stringify(res.sheetsInfo))
        }else{
          Toast.fail(res.msg)
        }
      })
    },
    getOrdersInfo(sheetIDs) {
      let params = {
        sheetIDs: sheetIDs
      }
      ApiGetOrdersInfoToAssignVan(params).then((res) => {
        if (res.result === "OK") {
          this.assignVanInfo.sumSheet = res.sumSheet
          var itemList = []
          this.assignVanInfo.sumSheet.sheetRows.forEach(row => {
            row.sheet_order_quantity = row.quantity
            row.sheet_order_unit_factor = row.unit_factor
            row.sheet_order_unit_no = row.unit_no
            row.main_image =""
            if(row.item_images){
              const itemImagesObj = JSON.parse(row.item_images)
              row.main_image = globalVars.obs_server_uri +'/' +itemImagesObj.tiny
            }
            row.qtyChanged = false
            itemList.push(row.item_id)
          })
          var orderStatus = {
            zd: '已转单',
            xd: '已下单',
            dd: '已打单',
            hk: '已回库',
            zc: '已装车'
          }
          res.sheets.forEach((item) => {
            item.order_status = orderStatus[item.order_status]
          })
          this.assignVanInfo.sheetsInfo = res.sheets
          this.newSheetsInfo = JSON.parse(JSON.stringify(res.sheets))
          this.assignVanInfo.sheetsInfo.forEach(sheet => {
            sheet.sheetRows.forEach(row=>{
              row.sheet_order_quantity = row.quantity
            })
          })
        }else{
          Toast.fail(res.msg)
          setTimeout(()=>{
            this.$router.back()
          },1000)
          
          //myGoBack(this.$router)
        }
      })
    },
    selectSenders(index) {
      this.$refs.sendersCheckboxes[index].toggle();
      setTimeout(() => {
        var ids = [];
        var names = [];
        this.selectedSenders.forEach(sender => {
          ids.push(sender.oper_id);
          names.push(sender.oper_name);
        });
        this.sheet.senders_id = ids.join(',');
        this.sheet.senders_name = names.join(',');
      }, 200);
    },
    onSelectSender() {
      if (this.sheet.approve_time) return;
      if(this.is_sender&&!this.canSelectSender){
        Toast.fail('您没有修改送货员的权限')
        return
      } 
      this.bPopupSenderPicker = true;
      var ids = this.sheet.senders_id.split(',');
      var names = this.sheet.senders_name.split(',');
      for (let i = 0; i < this.senderList.length; i++) {
        const el = this.senderList[i];
        var exist = false;
        ids.some(id => {
          if (id == el.oper_id) {
            exist = true;
            return;
          }
        })
        if (exist) {
          setTimeout(() => {
            this.$refs.sendersCheckboxes[i].toggle(true);
          }, 200);
        }
      }
    },
    getOtherInfo() {
      let branchlist = {};
      GetBranchList(branchlist).then((res) => {
        if (res.result === "OK") {
          branchlist = res.data
          branchlist.forEach(item => {
            var branchRights = this.$store.state.operInfo.branchRights
              branchRights.forEach(branch => {
                if (item.branch_id === branch.branch_id && branch.sheet_dr === 'True') {
                  let branchPosition = JSON.parse(item.branch_position)
                  let newBranchPosition = []
                  branchPosition.forEach(e=>{
                    if(e.branch_position !=="0"){
                      newBranchPosition.push(e)
                    }
                  })
                  this.branchList.push({
                    branch_id:item.branch_id,
                    branch_name:item.branch_name,
                    branch_position:newBranchPosition,
                    branch_type:item.branch_type,
                    negative_stock_accordance:item.negative_stock_accordance,
                  })
                  if (item.branch_type === "truck") {
                    this.toBranchList.push(item)
                  }
                }
              })
            // if (item.branch_type === "truck") {
            //   // this.toBranchList.push(item)
            //   var branchRights = this.$store.state.operInfo.branchRights
            //   branchRights.forEach(branch => {
            //     if (item.branch_id === branch.branch_id && branch.sheet_dr === 'True') {
            //       this.toBranchList.push(item)
            //     }
            //   })
            // }
          })
        }
      })
      GetSenders().then((res) => {
        if (res.result === "OK") {
          this.senderList = res.data
        }
      })
    },
    handleMoveSheetStoreMoveOut() {
      if (this.sheet.approve_time) return
      return Toast.fail('无法修改订单仓库')
    },
    handleMoveSheetStoreMoveIn() {
      if (this.sheet.approve_time) return
      this.popToBranchList = true;
    },
    async getVanStock(){
      var itemsInfo = this.assignVanInfo.sumSheet.sheetRows
      var items_id = ''
      itemsInfo.forEach(row=>{
        if(items_id!='')items_id +=','
        items_id += row.item_id
      })
      let params ={
        vanID:this.vehicle_id,
        items_id:items_id
      }
      ApiGetVanStock(params).then((res)=>{
        if(res.result=="OK"){
          this.vanStock = res.itemsStock
        }
      })
    },
    async onToBranchSelected(value) {
      this.vehicle_id = value.branch_id;
      this.vehicle_name = value.branch_name;
      await this.getVanStock()
      this.popToBranchList = false;
    },
    assignVan(type) {
      debugger
      var sumSheet = this.$refs.itemsView.sumSheet
      var sheets = this.assignVanInfo.sheetsInfo
      // var sheets = this.assignVanInfo.sheetsInfo
      // sheets.forEach(sheet=>{
      //   sheet.sheetRows.forEach(row=>{
      //     row.sheet_order_quantity = row.quantity
      //   })
      // })
     
      var senders = []
      if (this.sheet.senders_id === '') {
        if (this.is_sender) {
          senders.push(this.$store.state.operInfo.oper_id + ',' + this.$store.state.operInfo.oper_name)
        } else {
          if (this.selectedSenders.length === 0) return Toast.fail('请安排送货员')
          this.selectedSenders.forEach(sender => {
            senders.push(sender.oper_id + ',' + sender.oper_name)
          })
        }
      } else {
        var sendersID = this.sheet.senders_id.split(',')
        sendersID.forEach(id => {
          this.senderList.forEach(sender => {
            if (id === sender.oper_id) {
              senders.push(sender.oper_id + ',' + sender.oper_name)
            }
          })
        })
      }

      if (!this.vehicle_id) return Toast.fail('必须指定车辆')
      let params = {
        sheetIDs: this.assignVanInfo.sheetIDs,
        moveStock: this.moveStock,
        sumSheet: sumSheet,
        vanID: this.vehicle_id,
        senders: senders,
        sheets: sheets,
        newSheets: this.newSheetsInfo,
        fromApp: true,
        submitType: type,
        op_id: this.sheet.op_id,
        op_no: this.sheet.op_no

      }
      if(this.assignType=="changeVan"){
        params.newVanID=params.vanID
        params.newSenders= params.senders
        ChangeVan(params).then((res)=>{
          if(res.result==="OK"){
            Toast.success("审核成功");
          }else{
            Toast.fail(res.msg)
            return 
          }
          var assignSheet = res.cSheet
          this.sheet.op_no = assignSheet.sheet_no
          this.sheet.op_id = assignSheet.sheet_id
          if (type === 'approve') {
              this.sheet.approve_time = assignSheet.happen_time
              
              this.isPrinting = false
            }
        })
      }else{
        this.isApproveing = true
        AssignVan(params).then((res) => {
          if (res.result === "OK") {
            if (type === 'save') {
              Toast.success("保存成功");
            } else {
              Toast.success("审核成功");
            }
            window.g_objPrePage.clearSubmitList()
            
            var assignSheet = res.aSheet
            this.sheet.op_no = assignSheet.sheet_no
            if (type === 'save') this.isPrinting = false
            if (type === 'approve') {
              this.sheet.approve_time = res.happen_time
              this.isApproveing = true
              this.isPrinting = false
            }
            this.sheet.op_id = res.op_id

          } 
          else 
          {
            Toast.fail(res.msg)
            this.isApproveing = false
          }
        })
      }
    },
    cancelAssignVan() {
      Dialog.confirm({
        title: "撤销装车",
        message: "请确认是否撤销?",
        width:"320px"
      }).then(() => {
        var sheets = this.assignVanInfo.sheetsInfo
        var sumSheet = this.$refs.itemsView.sumSheet
        // sumSheet.forEach(sheet=>{
        //   sheet.SheetRows.forEach(row=>{
        //     row.sheet_order_quantity = row.quantity
        //   })
        // })
        let params = {
          moveStock: this.moveStock,
          redBrief: '',
          op_id: this.sheet.op_id,
          sumSheet: sumSheet,
          sheets:sheets,
          fromApp: true
        }
        
        CancelAssignVan(params).then((res) => {
          if (res.result === "OK") {
            Toast.success("撤销成功")
            this.sheet.red_flag = '1'
            this.isApproveing = false

          } else {
            Toast.fail(res.msg)
          }
        })
      })
    },
    reAssignVan() {
      Dialog.confirm({
        title: "重新装车",
        message: "请确认是否重新装车?",
        width:"320px"
      }).then(() => {
        this.sheet.approve_time = ''
        this.sheet.op_no = ''
        this.sheet.red_flag = ''
        this.sheet.op_id = ''
        this.isApproveing = false
        this.isSaveing = false

      })
    },
    printAssignVan() {
      if (!this.moveStock) {
        Toast.fail("未转移库存")
        return
      }
      this.isPrinting = true
      var sheet = this.sheet
      sheet.sheetsInfo = this.assignVanInfo.sheetsInfo
      sheet.sumSheet = this.assignVanInfo.sumSheet
      sheet.from_branch_name = this.assignVanInfo.branchName
      sheet.to_branch_name = this.vehicle_name
      var imageBase64 = null;
      Printing.printAssignvan(sheet, this.printBarcodeStyle, this.printBarcodePic, imageBase64, res => {
        this.isPrinting = false
        if (res.result == "OK") {
          Toast.success("打印成功");
        } else {
          Toast.fail(res.msg);
        }
      });
      this.isPrinting = false
    },
    deleteOrderSheet(id){
      var sheetIDs =""
      this.assignVanInfo.sheetsInfo.forEach(sht =>{
        if(sht.sheet_id !=id){
          if(sheetIDs!="")sheetIDs +=","
          sheetIDs+= sht.sheet_id
        }
      })
      if(sheetIDs!=""){
        this.getOrdersInfo(sheetIDs)
      }else{
        this.assignVanInfo=
        {
        sheetsInfo: [],
        sumSheet: {},
        branchName: '',
        sheetIDs: ''
        }
        this.sheet= {
        approve_time: '',
        sheet_no: '',
        red_flag: '',
        senders_id: '',
        senders_name: '',
        op_id: ''
      }
      }

    }
  },
}
</script>
<style lang="less" scoped>
@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
};
@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
/deep/ .van-tabs__content{
  height:calc(100% - 50px);
  .van-tab__pane{
    height: 100%;
  }
}
.public_query_title {
  background: #ffffff;
}
.public_query_title_t {
  height: 25px;
  line-height: 25px;
  font-size: 15px;
  color: #000000;
  padding: 0 10px 5px;
  @flex_a_bw();
  i {
    height: 18px;
    border: 1px solid red;
    display: inline-block;
    font-size: 12px;
    font-style: normal;
    line-height: 20px;
    padding: 0 5px;
    border-radius: 4px;
    margin-left: 10px;
  }
}
.public_query_wrapper {
  .public_query_titleSrc_item {
    width: 50%;
    height: 100%;
    overflow: hidden;
    position: relative;
    vertical-align: top;
    border: none;
    padding: 10px 0 3px 0;

    div {
      height: 100%;
      width: calc(100% - 40px);
      padding: 0 30px 0 10px;
      border: none;
      font-size: 15px;
      line-height: 35px;
      color: #333333;
      text-align: left;
    }
    input {
      height: 100%;
      width: calc(100% - 60px);
      padding: 0 10px 4px 0px;
      border: none;
      font-size: 15px;
      line-height: 35px;
      color: #333333;
      vertical-align: top;
      border-bottom: 1px solid #ddd;
      text-align: right;
    }
    .van-icon {
      position: absolute;
      left: 5px;
      top: 0;
      bottom: -10px;
      width: 30px;
      text-align: center;
      font-size: 22px;
      @flex_a_j();
      color: #aaa;
      background-color: #ffffff;
    }
  }
}
.approved_reded {
  position: absolute;
  left: 25px;
  top: 148px;
  margin-top: -40px;
  z-index: 99999;
}
.sheet_state {
  position: relative;
  // z-index: 999;
  // position: fixed;
}
.approved {
  position: absolute;
  width: 105px;
  height: 60px;
  top: -15px;
  left: 10px;
}
.reded {
  width: 105px;
  height: 60px;
  // top: 110px;
  // left: 50px;
}
</style>